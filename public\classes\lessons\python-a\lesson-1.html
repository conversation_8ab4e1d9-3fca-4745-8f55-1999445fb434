<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Bài 1: <PERSON><PERSON><PERSON>ng <PERSON>n <PERSON>ới Kỷ <PERSON>uyên <PERSON> và Thế Giới Lập Trình - Python A</title>
    <link rel="stylesheet" href="../../assets/css/styles.css">
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <style>
        .lesson-container {
            max-width: 1000px;
            margin: 120px auto 50px;
            padding: 0 20px;
        }
        
        .lesson-header {
            text-align: center;
            margin-bottom: 40px;
            padding: 30px;
            background: linear-gradient(135deg, #4285F4, #ff7aa8);
            color: white;
            border-radius: 15px;
        }
        
        .lesson-header h1 {
            font-size: 2.2rem;
            margin-bottom: 15px;
        }
        
        .lesson-header p {
            font-size: 1.1rem;
            opacity: 0.9;
        }
        
        .lesson-content {
            background: white;
            border-radius: 15px;
            box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
            padding: 40px;
            margin-bottom: 30px;
        }
        
        .lesson-section {
            margin-bottom: 40px;
        }
        
        .lesson-section h2 {
            color: #4285F4;
            font-size: 1.8rem;
            margin-bottom: 20px;
            border-bottom: 2px solid #f0f0f0;
            padding-bottom: 10px;
        }
        
        .lesson-section h3 {
            color: #333;
            font-size: 1.4rem;
            margin: 25px 0 15px;
        }
        
        .lesson-section ul {
            margin: 15px 0;
            padding-left: 25px;
        }
        
        .lesson-section li {
            margin-bottom: 8px;
            line-height: 1.6;
        }
        
        .lesson-section p {
            line-height: 1.7;
            margin-bottom: 15px;
        }
        
        .slide-link {
            display: inline-block;
            background: #4285F4;
            color: white;
            padding: 15px 25px;
            border-radius: 8px;
            text-decoration: none;
            margin: 20px 0;
            font-weight: 500;
            transition: background-color 0.3s;
        }
        
        .slide-link:hover {
            background: #3367D6;
            color: white;
        }
        
        .objectives-box {
            background: #f8f9fa;
            border-left: 4px solid #4285F4;
            padding: 20px;
            margin: 20px 0;
            border-radius: 0 8px 8px 0;
        }
        
        .back-link {
            display: inline-block;
            color: #4285F4;
            text-decoration: none;
            margin-bottom: 20px;
            font-weight: 500;
        }
        
        .back-link:hover {
            text-decoration: underline;
        }
    </style>
</head>
<body>
    <!-- Navigation Bar -->
    <header>
        <div class="container">
            <div class="logo">
                <img src="../../assets/images/logo.jpg" alt="Vthon Logo">
            </div>
            <nav>
                <ul>
                    <li><a href="../../index.html">Trang Chủ</a></li>
                    <li><a href="../../pages/classes.html">Lớp Học</a></li>
                    <li><a href="../../pages/achievements.html">Thành Tích</a></li>
                    <li><a href="../../pages/register.html">Đăng Ký</a></li>
                    <li><a href="../../pages/rankings.html">Bảng Xếp Hạng</a></li>
                    <li><a href="../../pages/research.html">Nghiên Cứu</a></li>
                    <li><a href="../../pages/account.html">Tài Khoản</a></li>
                </ul>
            </nav>
        </div>
    </header>

    <div class="lesson-container">
        <a href="../../pages/class-detail.html?class=python-a" class="back-link">
            <i class="fas fa-arrow-left"></i> Quay lại lớp Python - A
        </a>

        <div class="lesson-header">
            <h1>Bài 1: Chào Mừng Đến Với Kỷ Nguyên Số và Thế Giới Lập Trình</h1>
            <p>Khám phá thế giới Công nghệ Thông tin và Lập trình</p>
        </div>

        <div class="lesson-content">
            <div class="lesson-section">
                <h2>Slide Bài Học</h2>
                <a href="https://www.canva.com/design/DAGoEIUCxeE/f4G5xyktcg_yVi0fGpJlFw/edit?utm_content=DAGoEIUCxeE&utm_campaign=designshare&utm_medium=link2&utm_source=sharebutton" 
                   target="_blank" class="slide-link">
                    <i class="fas fa-presentation"></i> Xem Slide Bài Học (Canva)
                </a>
            </div>

            <div class="lesson-section">
                <h2>Mục Tiêu Bài Học</h2>
                <div class="objectives-box">
                    <ul>
                        <li>Hiểu được khái niệm Công nghệ Thông tin (CNTT) và vai trò của nó trong đời sống</li>
                        <li>Biết được một số lĩnh vực chính và cơ hội nghề nghiệp trong ngành CNTT</li>
                        <li>Hiểu được Lập trình là gì và tại sao nên học lập trình</li>
                    </ul>
                </div>
                <p><strong>Thời lượng dự kiến:</strong> 1 buổi</p>
            </div>

            <div class="lesson-section">
                <h2>Nội Dung Chính</h2>
                
                <h3>1. Tổng quan về Công nghệ Thông tin (CNTT)</h3>
                <ul>
                    <li><strong>CNTT là gì?</strong> Ví dụ thực tiễn (smartphone, Internet, mạng xã hội, game, ứng dụng học tập)</li>
                    <li><strong>Tầm quan trọng và ảnh hưởng</strong> của CNTT tới xã hội hiện đại</li>
                    <li><strong>Các lĩnh vực chính trong CNTT</strong> (sơ lược):
                        <ul>
                            <li>Phát triển phần mềm</li>
                            <li>Khoa học dữ liệu</li>
                            <li>An ninh mạng</li>
                            <li>Trí tuệ nhân tạo</li>
                            <li>Và nhiều lĩnh vực khác...</li>
                        </ul>
                    </li>
                </ul>

                <h3>2. Giới thiệu về Lập trình</h3>
                <ul>
                    <li><strong>Lập trình là gì?</strong> (Cách con người "nói chuyện" và "ra lệnh" cho máy tính)</li>
                    <li><strong>Lợi ích của việc học lập trình:</strong>
                        <ul>
                            <li>Phát triển tư duy logic</li>
                            <li>Nâng cao khả năng giải quyết vấn đề</li>
                            <li>Kích thích sự sáng tạo</li>
                        </ul>
                    </li>
                    <li><strong>Ngôn ngữ lập trình là gì?</strong> Giới thiệu sơ qua về sự đa dạng của các ngôn ngữ</li>
                </ul>
            </div>
        </div>
    </div>

    <!-- Footer -->
    <footer>
        <div class="container">
            <p>&copy; Kiến thức có đầu nhưng không có cuối - Vthon. Tất cả quyền được bảo lưu.</p>
        </div>
    </footer>

    <script src="../../assets/js/script.js"></script>
</body>
</html>
