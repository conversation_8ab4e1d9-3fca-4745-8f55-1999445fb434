<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><PERSON><PERSON><PERSON><PERSON></title>
    <link rel="stylesheet" href="../assets/css/styles.css">
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <style>
        .page-header {
            text-align: center;
            margin: 120px 0 50px;
        }
        
        .page-header h1 {
            font-size: 2.5rem;
            color: #333;
            margin-bottom: 15px;
        }
        
        .page-header p {
            color: #666;
            font-size: 1.2rem;
            max-width: 700px;
            margin: 0 auto;
        }
        
        .research-container {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 30px;
            padding: 20px 0 50px;
        }
        
        .research-card {
            background-color: white;
            border-radius: 15px;
            box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
            overflow: hidden;
            transition: transform 0.3s, box-shadow 0.3s;
            cursor: pointer;
        }
        
        .research-card:hover {
            transform: translateY(-8px);
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.15);
        }
        
        .research-image {
            height: 200px;
            overflow: hidden;
            position: relative;
        }
        
        .research-image img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            transition: transform 0.3s;
        }
        
        .research-card:hover .research-image img {
            transform: scale(1.05);
        }
        
        .research-category {
            position: absolute;
            top: 15px;
            left: 15px;
            background: linear-gradient(45deg, #4285F4, #ff7aa8);
            color: white;
            padding: 5px 12px;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 500;
        }
        
        .research-content {
            padding: 25px;
        }
        
        .research-title {
            font-size: 1.4rem;
            color: #333;
            margin-bottom: 10px;
            font-weight: 600;
            line-height: 1.3;
        }
        
        .research-meta {
            display: flex;
            align-items: center;
            gap: 15px;
            margin-bottom: 15px;
            color: #666;
            font-size: 0.9rem;
        }
        
        .research-meta i {
            color: #4285F4;
        }
        
        .research-excerpt {
            color: #555;
            line-height: 1.6;
            margin-bottom: 20px;
            display: -webkit-box;
            -webkit-line-clamp: 3;
            -webkit-box-orient: vertical;
            overflow: hidden;
        }
        
        .read-more-btn {
            display: inline-flex;
            align-items: center;
            gap: 8px;
            background: linear-gradient(45deg, #4285F4, #ff7aa8);
            color: white;
            padding: 10px 20px;
            border-radius: 25px;
            text-decoration: none;
            font-weight: 500;
            font-size: 0.9rem;
            transition: all 0.3s;
        }
        
        .read-more-btn:hover {
            transform: translateX(5px);
            box-shadow: 0 5px 15px rgba(66, 133, 244, 0.3);
        }
        
        .research-tags {
            margin-top: 15px;
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
        }
        
        .research-tag {
            background: #f8f9fa;
            color: #666;
            padding: 4px 10px;
            border-radius: 12px;
            font-size: 0.8rem;
            border: 1px solid #e9ecef;
        }
        
        /* Research Detail Modal */
        .research-modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.8);
            backdrop-filter: blur(5px);
        }
        
        .research-modal-content {
            background-color: white;
            margin: 2% auto;
            padding: 0;
            border-radius: 15px;
            width: 90%;
            max-width: 800px;
            max-height: 90vh;
            overflow-y: auto;
            position: relative;
        }
        
        .modal-header {
            position: relative;
            height: 250px;
            overflow: hidden;
        }
        
        .modal-header img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }
        
        .modal-close {
            position: absolute;
            top: 15px;
            right: 15px;
            background: rgba(0, 0, 0, 0.5);
            color: white;
            border: none;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            font-size: 1.2rem;
            cursor: pointer;
            transition: background 0.3s;
        }
        
        .modal-close:hover {
            background: rgba(0, 0, 0, 0.7);
        }
        
        .modal-body {
            padding: 30px;
        }
        
        .modal-title {
            font-size: 2rem;
            color: #333;
            margin-bottom: 15px;
            font-weight: 700;
        }
        
        .modal-meta {
            display: flex;
            align-items: center;
            gap: 20px;
            margin-bottom: 25px;
            color: #666;
            padding-bottom: 15px;
            border-bottom: 1px solid #eee;
        }
        
        .modal-content-text {
            line-height: 1.8;
            color: #444;
            font-size: 1.1rem;
        }
        
        .modal-content-text h3 {
            color: #4285F4;
            margin: 25px 0 15px;
            font-size: 1.3rem;
        }
        
        .modal-content-text p {
            margin-bottom: 15px;
        }
        
        .modal-content-text ul, .modal-content-text ol {
            margin: 15px 0;
            padding-left: 25px;
        }
        
        .modal-content-text li {
            margin-bottom: 8px;
        }
    </style>
</head>
<body>
    <!-- Navigation Bar -->
    <header>
        <div class="container">
            <div class="logo">
                <img src="../assets/images/logo.jpg" alt="Vthon Logo">
            </div>
            <nav>
                <ul>
                    <li><a href="../index.html">Trang Chủ</a></li>
                    <li><a href="classes.html">Lớp Học</a></li>
                    <li><a href="achievements.html">Thành Tích</a></li>
                    <li><a href="register.html">Đăng Ký</a></li>
                    <li><a href="rankings.html">Bảng Xếp Hạng</a></li>
                    <li><a href="research.html" class="active">Nghiên Cứu</a></li>
                    <li><a href="account.html">Tài Khoản</a></li>
                </ul>
            </nav>
        </div>
    </header>

    <!-- Research Section -->
    <section>
        <div class="container">
            <div class="page-header">
                <h1>Nghiên Cứu & Bài Báo</h1>
                <p>Khám phá những nghiên cứu mới nhất về Python, AI và Machine Learning từ cộng đồng Vthon</p>
            </div>
            
            <div class="research-container">
                <!-- No content placeholder -->
                <div style="grid-column: 1/-1; text-align: center; padding: 100px 20px; color: #666;">
                    <i class="fas fa-file-alt" style="font-size: 4rem; margin-bottom: 30px; color: #ddd;"></i>
                    <h3 style="font-size: 2rem; margin-bottom: 20px; color: #999;">Chưa có nội dung nghiên cứu</h3>
                    <p style="font-size: 1.2rem; max-width: 600px; margin: 0 auto; line-height: 1.6;">
                        Chúng tôi đang chuẩn bị những bài nghiên cứu chất lượng cao về Python, AI và Machine Learning.
                        Hãy quay lại sau để khám phá những kiến thức mới nhất từ cộng đồng Vthon.
                    </p>
                    <div style="margin-top: 30px;">
                        <a href="../index.html" style="display: inline-block; background: #4285F4; color: white; padding: 12px 25px; border-radius: 8px; text-decoration: none; font-weight: 500; transition: background-color 0.3s;">
                            <i class="fas fa-home"></i> Về Trang Chủ
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer>
        <div class="container">
            <p>&copy; Kiến thức có đầu nhưng không có cuối - Vthon.</p>
        </div>
    </footer>

    <!-- Research Detail Modal -->
    <div id="researchModal" class="research-modal">
        <div class="research-modal-content">
            <div class="modal-header">
                <img id="modalImage" src="" alt="">
                <button class="modal-close" onclick="closeResearchModal()">&times;</button>
            </div>
            <div class="modal-body">
                <h2 id="modalTitle" class="modal-title"></h2>
                <div id="modalMeta" class="modal-meta"></div>
                <div id="modalContent" class="modal-content-text"></div>
            </div>
        </div>
    </div>

    <script>
        // No research content yet - placeholder for future functionality
    </script>

    <script src="../assets/js/script.js"></script>
</body>
</html>
