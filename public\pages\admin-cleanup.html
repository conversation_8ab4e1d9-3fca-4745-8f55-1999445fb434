<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin Cleanup Tool - Classroom Web</title>
    <link rel="stylesheet" href="../assets/css/style.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }

        .admin-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .admin-container h1 {
            color: white;
            text-align: center;
            margin-bottom: 30px;
            font-size: 2.5rem;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .admin-section {
            background: white;
            border-radius: 15px;
            padding: 30px;
            margin-bottom: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            border: 1px solid rgba(255,255,255,0.2);
            backdrop-filter: blur(10px);
        }

        .admin-section h2 {
            color: #2c3e50;
            margin-bottom: 20px;
            border-bottom: 3px solid #3498db;
            padding-bottom: 15px;
            font-size: 1.8rem;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .admin-section h2 i {
            color: #3498db;
        }
        
        .orphaned-users-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
        }
        
        .orphaned-users-table th,
        .orphaned-users-table td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }
        
        .orphaned-users-table th {
            background-color: #f8f9fa;
            font-weight: 600;
        }
        
        .admin-btn {
            background: linear-gradient(135deg, #3498db, #2980b9);
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 25px;
            cursor: pointer;
            margin: 8px;
            transition: all 0.3s ease;
            font-weight: 600;
            font-size: 14px;
            box-shadow: 0 4px 15px rgba(52, 152, 219, 0.3);
            display: inline-flex;
            align-items: center;
            gap: 8px;
        }

        .admin-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(52, 152, 219, 0.4);
        }

        .admin-btn.danger {
            background: linear-gradient(135deg, #e74c3c, #c0392b);
            box-shadow: 0 4px 15px rgba(231, 76, 60, 0.3);
        }

        .admin-btn.danger:hover {
            box-shadow: 0 6px 20px rgba(231, 76, 60, 0.4);
        }

        .admin-btn.success {
            background: linear-gradient(135deg, #27ae60, #229954);
            box-shadow: 0 4px 15px rgba(39, 174, 96, 0.3);
        }

        .admin-btn.success:hover {
            box-shadow: 0 6px 20px rgba(39, 174, 96, 0.4);
        }

        .admin-btn:disabled {
            background: #bdc3c7;
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }

        .admin-actions {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            margin: 20px 0;
        }
        
        .loading {
            text-align: center;
            padding: 20px;
        }
        
        .loading i {
            font-size: 24px;
            animation: spin 1s linear infinite;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .stat-card {
            background: linear-gradient(135deg, #f8f9fa, #e9ecef);
            padding: 25px;
            border-radius: 15px;
            text-align: center;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            border: 1px solid rgba(255,255,255,0.2);
            transition: transform 0.3s ease;
        }

        .stat-card:hover {
            transform: translateY(-5px);
        }

        .stat-number {
            font-size: 2.5em;
            font-weight: bold;
            color: #3498db;
            text-shadow: 1px 1px 2px rgba(0,0,0,0.1);
        }

        .stat-label {
            color: #666;
            margin-top: 8px;
            font-weight: 500;
            font-size: 14px;
        }
        
        .success-message, .error-message {
            padding: 15px;
            margin: 15px 0;
            border-radius: 5px;
            display: none;
        }
        
        .success-message {
            background-color: rgba(76, 217, 100, 0.1);
            color: #2ca745;
            border: 1px solid #2ca745;
        }
        
        .error-message {
            background-color: rgba(231, 76, 60, 0.1);
            color: #e74c3c;
            border: 1px solid #e74c3c;
        }
        
        .progress-bar {
            width: 100%;
            height: 20px;
            background-color: #f0f0f0;
            border-radius: 10px;
            overflow: hidden;
            margin: 10px 0;
        }
        
        .progress-fill {
            height: 100%;
            background-color: #3498db;
            width: 0%;
            transition: width 0.3s ease;
        }
        
        .log-container {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 15px;
            max-height: 300px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
            margin-top: 15px;
        }
        
        .log-entry {
            margin-bottom: 5px;
            padding: 2px 0;
        }
        
        .log-entry.success {
            color: #28a745;
        }
        
        .log-entry.error {
            color: #dc3545;
        }
        
        .log-entry.info {
            color: #17a2b8;
        }
    </style>
</head>
<body>
    <nav class="navbar">
        <div class="nav-container">
            <div class="nav-logo">
                <a href="../index.html">Classroom Web</a>
            </div>
            <ul class="nav-menu">
                <li class="nav-item">
                    <a href="../index.html" class="nav-link">Trang Chủ</a>
                </li>
                <li class="nav-item">
                    <a href="account.html" class="nav-link">Tài Khoản</a>
                </li>
                <li class="nav-item">
                    <a href="#" class="nav-link" onclick="signOut()">Đăng Xuất</a>
                </li>
            </ul>
        </div>
    </nav>

    <main class="admin-container">
        <h1><i class="fas fa-tools"></i> Admin Cleanup Tool (Free Plan)</h1>
        
        <div id="adminContent" style="display: none;">
            <div class="admin-section">
                <h2><i class="fas fa-chart-bar"></i> Database Statistics</h2>
                <div class="stats-grid">
                    <div class="stat-card">
                        <div class="stat-number" id="totalFirestoreUsers">-</div>
                        <div class="stat-label">Total Firestore Users</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number" id="totalAuthUsers">-</div>
                        <div class="stat-label">Total Auth Users</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number" id="orphanedUsers">-</div>
                        <div class="stat-label">Orphaned Users</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number" id="totalAssignments">-</div>
                        <div class="stat-label">Total Assignments</div>
                    </div>
                </div>
            </div>

            <div class="admin-section">
                <h2><i class="fas fa-search"></i> Orphaned Data Scanner</h2>
                <p>This tool scans for user data in Firestore that doesn't have corresponding Firebase Authentication accounts.</p>
                
                <div class="admin-actions">
                    <button class="admin-btn" onclick="scanForOrphanedData()" id="scanBtn">
                        <i class="fas fa-search"></i> Scan for Orphaned Data
                    </button>
                    <button class="admin-btn success" onclick="exportOrphanedData()" id="exportBtn" disabled>
                        <i class="fas fa-download"></i> Export Orphaned Data
                    </button>
                    <button class="admin-btn danger" onclick="cleanupAllOrphaned()" id="cleanupAllBtn" disabled>
                        <i class="fas fa-trash"></i> Cleanup All Orphaned Data
                    </button>
                </div>
                
                <div id="successMessage" class="success-message"></div>
                <div id="errorMessage" class="error-message"></div>
                
                <div id="loadingIndicator" class="loading" style="display: none;">
                    <i class="fas fa-spinner"></i>
                    <p id="loadingText">Processing...</p>
                </div>
                
                <div id="progressContainer" style="display: none;">
                    <div class="progress-bar">
                        <div class="progress-fill" id="progressFill"></div>
                    </div>
                    <div id="progressText">0%</div>
                </div>
                
                <div id="orphanedUsersContainer" style="display: none;">
                    <h3>Orphaned Users Found</h3>
                    <table class="orphaned-users-table" id="orphanedUsersTable">
                        <thead>
                            <tr>
                                <th>UID</th>
                                <th>Email</th>
                                <th>Full Name</th>
                                <th>Created At</th>
                                <th>Assignments</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody id="orphanedUsersBody">
                            <!-- Orphaned users will be populated here -->
                        </tbody>
                    </table>
                </div>
                
                <div id="logContainer" class="log-container" style="display: none;">
                    <h4>Operation Log</h4>
                    <div id="logContent"></div>
                </div>
            </div>
        </div>
        
        <div id="accessDenied" style="display: none; text-align: center; padding: 50px;">
            <h2>Access Denied</h2>
            <p>You don't have admin privileges to access this page.</p>
            <a href="account.html" class="admin-btn">Go to Account</a>
        </div>
    </main>

    <!-- Firebase SDK -->
    <script type="module">
        import { initializeApp } from 'https://www.gstatic.com/firebasejs/11.8.1/firebase-app.js';
        import { getAuth, onAuthStateChanged, signOut as firebaseSignOut, deleteUser } from 'https://www.gstatic.com/firebasejs/11.8.1/firebase-auth.js';
        import { getFirestore, doc, getDoc, collection, getDocs, deleteDoc, query, limit } from 'https://www.gstatic.com/firebasejs/11.8.1/firebase-firestore.js';

        // Firebase configuration
        const firebaseConfig = {
            apiKey: "AIzaSyBKNo8y_MOKYc3f3UdNRFwcMgeLRW71WXA",
            authDomain: "classroom-web-48bc2.firebaseapp.com",
            projectId: "classroom-web-48bc2",
            storageBucket: "classroom-web-48bc2.firebasestorage.app",
            messagingSenderId: "************",
            appId: "1:************:web:48d5ffd5a0b2c6e043b73f",
            measurementId: "G-742XRP9E96"
        };

        // Initialize Firebase
        const app = initializeApp(firebaseConfig);
        const auth = getAuth(app);
        const db = getFirestore(app);

        // Global variables
        let currentOrphanedUsers = [];
        let isScanning = false;

        // Check admin access
        onAuthStateChanged(auth, async (user) => {
            if (user) {
                try {
                    const userDoc = await getDoc(doc(db, "users", user.uid));
                    const userData = userDoc.data();

                    // Check if user is admin
                    if (userData && (userData.isAdmin || userData.email === '<EMAIL>')) {
                        document.getElementById('adminContent').style.display = 'block';
                        document.getElementById('accessDenied').style.display = 'none';
                        loadInitialStats();
                    } else {
                        document.getElementById('adminContent').style.display = 'none';
                        document.getElementById('accessDenied').style.display = 'block';
                    }
                } catch (error) {
                    console.error('Error checking admin status:', error);
                    document.getElementById('adminContent').style.display = 'none';
                    document.getElementById('accessDenied').style.display = 'block';
                }
            } else {
                window.location.href = 'account.html';
            }
        });

        // Load initial statistics
        async function loadInitialStats() {
            try {
                // Count Firestore users
                const usersSnapshot = await getDocs(collection(db, "users"));
                document.getElementById('totalFirestoreUsers').textContent = usersSnapshot.size;

                // Count assignments
                let totalAssignments = 0;
                for (const userDoc of usersSnapshot.docs) {
                    const assignmentsSnapshot = await getDocs(collection(db, "users", userDoc.id, "assignments"));
                    totalAssignments += assignmentsSnapshot.size;
                }
                document.getElementById('totalAssignments').textContent = totalAssignments;

            } catch (error) {
                console.error('Error loading initial stats:', error);
            }
        }

        // Scan for orphaned data
        window.scanForOrphanedData = async function() {
            if (isScanning) return;

            isScanning = true;
            showLoading(true, 'Scanning for orphaned data...');
            hideMessages();
            showLog(true);
            clearLog();

            try {
                logMessage('Starting orphaned data scan...', 'info');

                // Get all users from Firestore
                const usersSnapshot = await getDocs(collection(db, "users"));
                const firestoreUsers = [];

                logMessage(`Found ${usersSnapshot.size} users in Firestore`, 'info');

                // Collect user data and count assignments
                for (const userDoc of usersSnapshot.docs) {
                    const userData = userDoc.data();
                    const assignmentsSnapshot = await getDocs(collection(db, "users", userDoc.id, "assignments"));

                    firestoreUsers.push({
                        uid: userDoc.id,
                        email: userData.email,
                        fullName: userData.fullName,
                        createdAt: userData.createdAt,
                        assignmentCount: assignmentsSnapshot.size,
                        data: userData
                    });
                }

                // Since we can't access Firebase Auth list on client side,
                // we'll use a different approach: check for users that haven't been active
                // or have specific markers indicating they should be cleaned up

                logMessage('Analyzing user data for cleanup candidates...', 'info');

                // For now, we'll identify potential orphaned users based on:
                // 1. Users with no recent activity
                // 2. Users marked for deletion
                // 3. Users with incomplete profiles that are old

                const orphanedUsers = firestoreUsers.filter(user => {
                    // Add your logic here to identify orphaned users
                    // For example: users created more than 30 days ago with no assignments
                    const createdDate = user.createdAt ? new Date(user.createdAt) : new Date();
                    const daysSinceCreation = (Date.now() - createdDate.getTime()) / (1000 * 60 * 60 * 24);

                    return (
                        (daysSinceCreation > 30 && user.assignmentCount === 0) ||
                        user.data.markedForDeletion ||
                        (!user.fullName && daysSinceCreation > 7)
                    );
                });

                currentOrphanedUsers = orphanedUsers;

                // Update stats
                document.getElementById('orphanedUsers').textContent = orphanedUsers.length;

                if (orphanedUsers.length > 0) {
                    displayOrphanedUsers(orphanedUsers);
                    document.getElementById('cleanupAllBtn').disabled = false;
                    document.getElementById('exportBtn').disabled = false;
                    logMessage(`Found ${orphanedUsers.length} potential orphaned users`, 'success');
                    showSuccess(`Scan complete: Found ${orphanedUsers.length} potential orphaned users`);
                } else {
                    document.getElementById('orphanedUsersContainer').style.display = 'none';
                    document.getElementById('cleanupAllBtn').disabled = true;
                    document.getElementById('exportBtn').disabled = true;
                    logMessage('No orphaned users found', 'success');
                    showSuccess('Scan complete: No orphaned users found');
                }

            } catch (error) {
                console.error('Error scanning for orphaned data:', error);
                logMessage(`Error during scan: ${error.message}`, 'error');
                showError('Error scanning for orphaned data: ' + error.message);
            } finally {
                showLoading(false);
                isScanning = false;
            }
        };

        // Display orphaned users in table
        function displayOrphanedUsers(users) {
            const tbody = document.getElementById('orphanedUsersBody');
            tbody.innerHTML = '';

            users.forEach(user => {
                const row = document.createElement('tr');
                row.innerHTML = `
                    <td>${user.uid}</td>
                    <td>${user.email || 'N/A'}</td>
                    <td>${user.fullName || 'N/A'}</td>
                    <td>${user.createdAt ? new Date(user.createdAt).toLocaleDateString() : 'N/A'}</td>
                    <td>${user.assignmentCount}</td>
                    <td>
                        <button class="admin-btn danger" onclick="cleanupSingleUser('${user.uid}')">
                            <i class="fas fa-trash"></i> Delete
                        </button>
                    </td>
                `;
                tbody.appendChild(row);
            });

            document.getElementById('orphanedUsersContainer').style.display = 'block';
        }

        // Cleanup single user
        window.cleanupSingleUser = async function(uid) {
            const userInfo = currentOrphanedUsers.find(u => u.uid === uid);
            const userEmail = userInfo ? userInfo.email : 'Unknown';

            if (!confirm(`⚠️ CẢNH BÁO: Bạn có chắc chắn muốn xóa tài khoản này?\n\nUID: ${uid}\nEmail: ${userEmail}\n\n✅ Sẽ xóa từ Firestore Database\n❌ Không thể xóa từ Firebase Authentication (cần quyền admin server)\n\nHành động này không thể hoàn tác!`)) {
                return;
            }

            showLoading(true, `🗑️ Đang xóa tài khoản ${userEmail}...`);
            hideMessages();
            showLog(true);

            try {
                logMessage(`🚀 Bắt đầu xóa tài khoản: ${uid} (${userEmail})`, 'info');

                // Delete user assignments
                const assignmentsSnapshot = await getDocs(collection(db, "users", uid, "assignments"));
                logMessage(`📝 Tìm thấy ${assignmentsSnapshot.size} bài tập`, 'info');
                for (const assignmentDoc of assignmentsSnapshot.docs) {
                    await deleteDoc(assignmentDoc.ref);
                    logMessage(`✅ Đã xóa bài tập: ${assignmentDoc.id}`, 'success');
                }

                // Delete other subcollections if they exist
                const subcollections = ['progress', 'achievements', 'notifications'];
                for (const subcollectionName of subcollections) {
                    try {
                        const subcollectionSnapshot = await getDocs(collection(db, "users", uid, subcollectionName));
                        if (subcollectionSnapshot.size > 0) {
                            logMessage(`📊 Tìm thấy ${subcollectionSnapshot.size} ${subcollectionName}`, 'info');
                            for (const subDoc of subcollectionSnapshot.docs) {
                                await deleteDoc(subDoc.ref);
                                logMessage(`✅ Đã xóa ${subcollectionName}: ${subDoc.id}`, 'success');
                            }
                        }
                    } catch (error) {
                        logMessage(`⚠️ Không thể xóa ${subcollectionName}: ${error.message}`, 'error');
                    }
                }

                // Delete main user document
                await deleteDoc(doc(db, "users", uid));
                logMessage(`✅ Đã xóa tài liệu người dùng chính: ${uid}`, 'success');
                logMessage(`⚠️ LƯU Ý: Tài khoản Firebase Authentication vẫn tồn tại và cần được xóa thủ công từ Firebase Console`, 'info');

                // Remove from current list and update display
                currentOrphanedUsers = currentOrphanedUsers.filter(user => user.uid !== uid);
                displayOrphanedUsers(currentOrphanedUsers);
                document.getElementById('orphanedUsers').textContent = currentOrphanedUsers.length;

                if (currentOrphanedUsers.length === 0) {
                    document.getElementById('cleanupAllBtn').disabled = true;
                    document.getElementById('exportBtn').disabled = true;
                }

                showSuccess(`✅ Đã xóa thành công dữ liệu Firestore cho ${userEmail}. ⚠️ Lưu ý: Tài khoản Firebase Auth cần xóa thủ công từ Firebase Console.`);

            } catch (error) {
                console.error('Error cleaning up user:', error);
                logMessage(`❌ Lỗi khi xóa tài khoản ${uid}: ${error.message}`, 'error');
                showError('❌ Lỗi khi xóa tài khoản: ' + error.message);
            } finally {
                showLoading(false);
            }
        };

        // Cleanup all orphaned users
        window.cleanupAllOrphaned = async function() {
            if (currentOrphanedUsers.length === 0) {
                showError('No orphaned users to cleanup');
                return;
            }

            if (!confirm(`Are you sure you want to delete ALL ${currentOrphanedUsers.length} orphaned users? This action cannot be undone.`)) {
                return;
            }

            showLoading(true, 'Cleaning up all orphaned users...');
            showProgress(true);
            hideMessages();

            let successCount = 0;
            let errorCount = 0;

            try {
                for (let i = 0; i < currentOrphanedUsers.length; i++) {
                    const user = currentOrphanedUsers[i];
                    const progress = ((i + 1) / currentOrphanedUsers.length) * 100;
                    updateProgress(progress, `Processing ${i + 1}/${currentOrphanedUsers.length}: ${user.uid}`);

                    try {
                        // Delete user assignments
                        const assignmentsSnapshot = await getDocs(collection(db, "users", user.uid, "assignments"));
                        for (const assignmentDoc of assignmentsSnapshot.docs) {
                            await deleteDoc(assignmentDoc.ref);
                        }

                        // Delete subcollections
                        const subcollections = ['progress', 'achievements', 'notifications'];
                        for (const subcollectionName of subcollections) {
                            try {
                                const subcollectionSnapshot = await getDocs(collection(db, "users", user.uid, subcollectionName));
                                for (const subDoc of subcollectionSnapshot.docs) {
                                    await deleteDoc(subDoc.ref);
                                }
                            } catch (error) {
                                // Subcollection might not exist
                            }
                        }

                        // Delete main user document
                        await deleteDoc(doc(db, "users", user.uid));

                        successCount++;
                        logMessage(`Successfully cleaned up user: ${user.uid}`, 'success');

                    } catch (error) {
                        errorCount++;
                        logMessage(`Error cleaning up user ${user.uid}: ${error.message}`, 'error');
                    }
                }

                // Clear the display
                currentOrphanedUsers = [];
                document.getElementById('orphanedUsersContainer').style.display = 'none';
                document.getElementById('cleanupAllBtn').disabled = true;
                document.getElementById('exportBtn').disabled = true;
                document.getElementById('orphanedUsers').textContent = '0';

                showSuccess(`Cleanup complete: ${successCount} users cleaned up successfully, ${errorCount} errors`);

            } catch (error) {
                console.error('Error during bulk cleanup:', error);
                showError('Error during bulk cleanup: ' + error.message);
            } finally {
                showLoading(false);
                showProgress(false);
            }
        };

        // Export orphaned data
        window.exportOrphanedData = function() {
            if (currentOrphanedUsers.length === 0) {
                showError('No orphaned users to export');
                return;
            }

            const dataStr = JSON.stringify(currentOrphanedUsers, null, 2);
            const dataBlob = new Blob([dataStr], {type: 'application/json'});

            const link = document.createElement('a');
            link.href = URL.createObjectURL(dataBlob);
            link.download = `orphaned-users-${new Date().toISOString().split('T')[0]}.json`;
            link.click();

            showSuccess(`Exported ${currentOrphanedUsers.length} orphaned users to JSON file`);
        };

        // Utility functions
        function showLoading(show, text = 'Processing...') {
            document.getElementById('loadingIndicator').style.display = show ? 'block' : 'none';
            document.getElementById('loadingText').textContent = text;
            document.getElementById('scanBtn').disabled = show;
        }

        function showProgress(show) {
            document.getElementById('progressContainer').style.display = show ? 'block' : 'none';
            if (!show) {
                updateProgress(0, '');
            }
        }

        function updateProgress(percent, text) {
            document.getElementById('progressFill').style.width = percent + '%';
            document.getElementById('progressText').textContent = `${Math.round(percent)}% - ${text}`;
        }

        function showLog(show) {
            document.getElementById('logContainer').style.display = show ? 'block' : 'none';
        }

        function clearLog() {
            document.getElementById('logContent').innerHTML = '';
        }

        function logMessage(message, type = 'info') {
            const logContent = document.getElementById('logContent');
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = document.createElement('div');
            logEntry.className = `log-entry ${type}`;
            logEntry.textContent = `[${timestamp}] ${message}`;
            logContent.appendChild(logEntry);
            logContent.scrollTop = logContent.scrollHeight;
        }

        function showSuccess(message) {
            const successMsg = document.getElementById('successMessage');
            successMsg.textContent = message;
            successMsg.style.display = 'block';
            setTimeout(() => {
                successMsg.style.display = 'none';
            }, 5000);
        }

        function showError(message) {
            const errorMsg = document.getElementById('errorMessage');
            errorMsg.textContent = message;
            errorMsg.style.display = 'block';
        }

        function hideMessages() {
            document.getElementById('successMessage').style.display = 'none';
            document.getElementById('errorMessage').style.display = 'none';
        }

        // Sign out function
        window.signOut = async function() {
            try {
                await firebaseSignOut(auth);
                window.location.href = 'account.html';
            } catch (error) {
                console.error('Error signing out:', error);
            }
        };
    </script>
</body>
</html>
