{"name": "classroom-web", "version": "1.0.0", "description": "Online Classroom Web Application", "main": "index.html", "scripts": {"build": "echo 'No build process needed for static site'", "start": "firebase serve", "deploy": "firebase deploy --only hosting"}, "repository": {"type": "git", "url": "git+https://github.com/sunniie/classroom-web.git"}, "keywords": ["education", "classroom", "firebase", "javascript", "html", "css"], "author": "<PERSON><PERSON>", "license": "MIT", "bugs": {"url": "https://github.com/sunniie/classroom-web/issues"}, "homepage": "https://github.com/sunniie/classroom-web#readme", "devDependencies": {"firebase-tools": "^13.0.0"}}