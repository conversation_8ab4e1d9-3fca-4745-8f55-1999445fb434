<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Python - B | Classroom Web</title>
    <link rel="stylesheet" href="../assets/css/style.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        .class-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 60px 0;
            text-align: center;
        }

        .class-header h1 {
            font-size: 3rem;
            margin-bottom: 10px;
        }

        .class-header p {
            font-size: 1.2rem;
            opacity: 0.9;
        }

        .class-info {
            background: white;
            padding: 40px 0;
        }

        .info-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 30px;
            margin-bottom: 40px;
        }

        .info-card {
            background: #f8f9fa;
            padding: 25px;
            border-radius: 10px;
            text-align: center;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }

        .info-card i {
            font-size: 2.5rem;
            color: #667eea;
            margin-bottom: 15px;
        }

        .info-card h3 {
            color: #333;
            margin-bottom: 10px;
        }

        .access-denied {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            color: #856404;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
            margin: 20px 0;
        }

        .access-granted {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
            margin: 20px 0;
        }

        .lessons-section {
            background: #f8f9fa;
            padding: 40px 0;
        }

        .lesson-card {
            background: white;
            border-radius: 10px;
            padding: 25px;
            margin-bottom: 20px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }

        .lesson-card h3 {
            color: #667eea;
            margin-bottom: 15px;
        }

        .meet-link {
            display: inline-block;
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            padding: 12px 25px;
            border-radius: 25px;
            text-decoration: none;
            margin-top: 15px;
            transition: transform 0.3s ease;
        }

        .meet-link:hover {
            transform: translateY(-2px);
        }

        .empty-class-notice {
            background: #f8f9fa;
            border: 2px dashed #dee2e6;
            padding: 40px;
            text-align: center;
            border-radius: 10px;
            margin: 20px 0;
        }

        .empty-class-notice i {
            font-size: 3rem;
            color: #6c757d;
            margin-bottom: 20px;
        }

        .empty-class-notice h3 {
            color: #6c757d;
            margin-bottom: 15px;
        }
    </style>
</head>
<body>
    <!-- Navigation Bar -->
    <header>
        <div class="container">
            <div class="logo">
                <img src="../assets/images/logo.jpg" alt="Vthon Logo">
            </div>
            <nav>
                <ul>
                    <li><a href="../index.html">Trang Chủ</a></li>
                    <li><a href="index.html">Lớp Học</a></li>
                    <li><a href="../achievements/">Thành Tích</a></li>
                    <li><a href="../auth/register.html">Đăng Ký</a></li>
                    <li><a href="../rankings/">Bảng Xếp Hạng</a></li>
                    <li><a href="../research/">Nghiên Cứu</a></li>
                    <li><a href="../auth/">Tài Khoản</a></li>
                </ul>
            </nav>
        </div>
    </header>

    <!-- Class Header -->
    <section class="class-header">
        <div class="container">
            <h1><i class="fab fa-python"></i> Python - B</h1>
            <p>Lớp học Python cơ bản đến nâng cao - Thứ 2 & Thứ 4</p>
        </div>
    </section>

    <!-- Access Control Message -->
    <section class="class-info">
        <div class="container">
            <div id="accessMessage" style="display: none;"></div>
            
            <div id="classContent" style="display: none;">
                <!-- Empty Class Notice -->
                <div class="empty-class-notice">
                    <i class="fas fa-users-slash"></i>
                    <h3>Lớp học chưa có học viên</h3>
                    <p>Lớp Python - B hiện tại chưa có học viên nào đăng ký. Hãy là người đầu tiên tham gia lớp học này!</p>
                </div>

                <!-- Class Information -->
                <div class="info-grid">
                    <div class="info-card">
                        <i class="fas fa-calendar-alt"></i>
                        <h3>Lịch Học</h3>
                        <p>Thứ 2 - Thứ 4<br>19:30 - 21:00</p>
                    </div>
                    <div class="info-card">
                        <i class="fas fa-users"></i>
                        <h3>Học Viên</h3>
                        <p id="studentCount">Đang tải...</p>
                    </div>
                    <div class="info-card">
                        <i class="fas fa-video"></i>
                        <h3>Google Meet</h3>
                        <p style="color: #6c757d;">Chưa có link học</p>
                        <small>Link sẽ được cung cấp khi có học viên</small>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Lessons Section -->
    <section class="lessons-section" id="lessonsSection" style="display: none;">
        <div class="container">
            <h2 style="text-align: center; margin-bottom: 40px;">Nội Dung Bài Học</h2>
            
            <div class="lesson-card">
                <h3><i class="fas fa-play-circle"></i> Bài 1: Giới thiệu về IT và Lập trình</h3>
                <p><strong>Mục tiêu:</strong> Hiểu được tổng quan về ngành Công nghệ Thông tin và vai trò của lập trình trong cuộc sống hiện đại.</p>
                <p><strong>Nội dung chính:</strong></p>
                <ul>
                    <li>Ngành IT là gì? Các lĩnh vực trong IT</li>
                    <li>Lập trình là gì? Tại sao cần học lập trình?</li>
                    <li>Các ngôn ngữ lập trình phổ biến</li>
                    <li>Giới thiệu về Python và ứng dụng</li>
                    <li>Cài đặt môi trường lập trình Python</li>
                </ul>
                <p><strong>Thời lượng:</strong> 90 phút</p>
                <p><strong>Trạng thái:</strong> <span style="color: #6c757d;">Chưa bắt đầu (chờ học viên)</span></p>
            </div>

            <div class="lesson-card">
                <h3><i class="fas fa-code"></i> Bài 2: Cú pháp cơ bản Python</h3>
                <p><strong>Nội dung:</strong> Biến, kiểu dữ liệu, toán tử cơ bản</p>
                <p><strong>Trạng thái:</strong> <span style="color: #6c757d;">Chưa mở</span></p>
            </div>

            <div class="lesson-card">
                <h3><i class="fas fa-code"></i> Bài 3: Cấu trúc điều khiển</h3>
                <p><strong>Nội dung:</strong> If-else, vòng lặp for, while</p>
                <p><strong>Trạng thái:</strong> <span style="color: #6c757d;">Chưa mở</span></p>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer>
        <div class="container">
            <p>&copy; Kiến thức có đầu nhưng không có cuối - Vthon.</p>
        </div>
    </footer>

    <!-- Firebase SDK -->
    <script type="module">
        import { initializeApp } from "https://www.gstatic.com/firebasejs/11.8.1/firebase-app.js";
        import { getAuth, onAuthStateChanged } from "https://www.gstatic.com/firebasejs/11.8.1/firebase-auth.js";
        import { getFirestore, doc, getDoc, collection, getDocs, query, where } from "https://www.gstatic.com/firebasejs/11.8.1/firebase-firestore.js";

        // Firebase configuration
        const firebaseConfig = {
            apiKey: "AIzaSyBKNo8y_MOKYc3f3UdNRFwcMgeLRW71WXA",
            authDomain: "classroom-web-48bc2.firebaseapp.com",
            projectId: "classroom-web-48bc2",
            storageBucket: "classroom-web-48bc2.firebasestorage.app",
            messagingSenderId: "446746787502",
            appId: "1:446746787502:web:48d5ffd5a0b2c6e043b73f",
            measurementId: "G-742XRP9E96"
        };

        // Initialize Firebase
        const app = initializeApp(firebaseConfig);
        const auth = getAuth(app);
        const db = getFirestore(app);

        // Check access permissions
        onAuthStateChanged(auth, async (user) => {
            const accessMessage = document.getElementById('accessMessage');
            const classContent = document.getElementById('classContent');
            const lessonsSection = document.getElementById('lessonsSection');

            if (user) {
                try {
                    const userDoc = await getDoc(doc(db, "users", user.uid));
                    const userData = userDoc.data();

                    // Check if user is admin
                    const isAdmin = userData && (userData.isAdmin || user.email === '<EMAIL>');
                    
                    // Check if user has access to Python-B class
                    const hasAccess = isAdmin || (userData && userData.courseClass === 'python-b');

                    if (hasAccess) {
                        // Grant access
                        accessMessage.innerHTML = `
                            <div class="access-granted">
                                <i class="fas fa-check-circle"></i>
                                <strong>Chào mừng ${isAdmin ? 'Admin' : 'học viên'}!</strong> 
                                Bạn có quyền truy cập vào lớp Python - B.
                                ${isAdmin ? '<br><small>Bạn đang ở chế độ Admin - có thể truy cập tất cả các lớp học.</small>' : ''}
                            </div>
                        `;
                        accessMessage.style.display = 'block';
                        classContent.style.display = 'block';
                        lessonsSection.style.display = 'block';

                        // Load student count
                        loadStudentCount();
                    } else {
                        // Deny access
                        const userClass = userData?.courseClass || 'chưa chọn lớp';
                        accessMessage.innerHTML = `
                            <div class="access-denied">
                                <i class="fas fa-exclamation-triangle"></i>
                                <strong>Không có quyền truy cập!</strong><br>
                                Bạn đã đăng ký lớp: <strong>${userClass}</strong><br>
                                Để truy cập lớp Python - B, vui lòng cập nhật thông tin lớp học trong trang 
                                <a href="account.html">Tài Khoản</a>.
                            </div>
                        `;
                        accessMessage.style.display = 'block';
                    }
                } catch (error) {
                    console.error('Error checking user permissions:', error);
                    accessMessage.innerHTML = `
                        <div class="access-denied">
                            <i class="fas fa-exclamation-triangle"></i>
                            <strong>Lỗi kiểm tra quyền truy cập!</strong><br>
                            Vui lòng thử lại sau hoặc liên hệ admin.
                        </div>
                    `;
                    accessMessage.style.display = 'block';
                }
            } else {
                // User not logged in
                accessMessage.innerHTML = `
                    <div class="access-denied">
                        <i class="fas fa-sign-in-alt"></i>
                        <strong>Vui lòng đăng nhập!</strong><br>
                        Bạn cần đăng nhập để truy cập nội dung lớp học.
                        <br><a href="account.html">Đăng nhập ngay</a>
                    </div>
                `;
                accessMessage.style.display = 'block';
            }
        });

        // Load student count for Python-B class
        async function loadStudentCount() {
            try {
                const usersQuery = query(collection(db, "users"), where("courseClass", "==", "python-b"));
                const querySnapshot = await getDocs(usersQuery);
                document.getElementById('studentCount').textContent = `${querySnapshot.size} học viên`;
            } catch (error) {
                console.error('Error loading student count:', error);
                document.getElementById('studentCount').textContent = 'Không thể tải';
            }
        }
    </script>

    <script src="../assets/js/script.js"></script>
</body>
</html>
