<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Vthon - Lớp học lập trình <PERSON> và AI</title>
    <link rel="stylesheet" href="assets/css/styles.css">
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
</head>
<body>
    <!-- Navigation Bar -->
    <header>
        <div class="container">
            <div class="logo">
                <img src="assets/images/logo.jpg" alt="Vthon Logo">
            </div>
            <nav>
                <ul>
                    <li><a href="index.html" class="active">Trang <PERSON>ủ</a></li>
                    <li><a href="pages/classes.html">Lớ<PERSON></a></li>
                    <li><a href="pages/achievements.html">Thành Tích</a></li>
                    <li><a href="pages/register.html">Đăng Ký</a></li>
                    <li><a href="pages/rankings.html">Bảng Xếp Hạng</a></li>
                    <li><a href="pages/research.html">Nghiên Cứu</a></li>
                    <li><a href="pages/account.html">Tài Khoản</a></li>
                </ul>
            </nav>
        </div>
    </header>

    <!-- Hero Section with Full Screen Background -->
    <section class="hero">
        <div class="hero-content">
            <h1>Chào mừng bạn đến với lớp học lập trình Vthon</h1>
            <p>Khám phá thế giới Python và AI cùng chúng tôi</p>
            <a href="pages/account.html" class="btn">Đăng Nhập</a>

            <!-- Contact Information -->
            <div class="contact-info">
                <h3>Thông tin liên lạc</h3>
                <div class="contact-grid">
                    <div class="contact-item">
                        <i class="fab fa-whatsapp"></i>
                        <div class="contact-details">
                            <h4>Zalo</h4>
                            <p>**********</p>
                        </div>
                    </div>
                    <div class="contact-item">
                        <i class="fas fa-envelope"></i>
                        <div class="contact-details">
                            <h4>Email</h4>
                            <p><EMAIL></p>
                        </div>
                    </div>
                    <div class="contact-item">
                        <a href="https://www.facebook.com/vinhle030904/" target="_blank" class="facebook-link">
                            <i class="fab fa-facebook"></i>
                            <div class="contact-details">
                                <h4>Facebook</h4>
                                <p>Liên hệ qua Facebook</p>
                            </div>
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer>
        <div class="container">
            <p>&copy; Kiến thức có đầu nhưng không có cuối - Vthon.</p>
        </div>
    </footer>

    <!-- Firebase SDK -->
    <script type="module">
        // Import the functions you need from the SDKs
        import { initializeApp } from "https://www.gstatic.com/firebasejs/11.8.1/firebase-app.js";
        import { getAnalytics } from "https://www.gstatic.com/firebasejs/11.8.1/firebase-analytics.js";
        import { getAuth, onAuthStateChanged } from "https://www.gstatic.com/firebasejs/11.8.1/firebase-auth.js";

        // Firebase configuration
        const firebaseConfig = {
            apiKey: "AIzaSyBKNo8y_MOKYc3f3UdNRFwcMgeLRW71WXA",
            authDomain: "classroom-web-48bc2.firebaseapp.com",
            projectId: "classroom-web-48bc2",
            storageBucket: "classroom-web-48bc2.firebasestorage.app",
            messagingSenderId: "446746787502",
            appId: "1:446746787502:web:48d5ffd5a0b2c6e043b73f",
            measurementId: "G-742XRP9E96"
        };

        // Initialize Firebase
        const app = initializeApp(firebaseConfig);
        const analytics = getAnalytics(app);
        const auth = getAuth(app);

        // Check if user is logged in
        onAuthStateChanged(auth, (user) => {
            const loginButton = document.querySelector('.hero .btn');
            
            if (user) {
                // User is logged in
                loginButton.textContent = 'Vào Trang Cá Nhân';
            } else {
                // User is not logged in
                loginButton.textContent = 'Đăng Nhập';
            }
        });
    </script>

    <script src="assets/js/script.js"></script>
</body>
</html> 