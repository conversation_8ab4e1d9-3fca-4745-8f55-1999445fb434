<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><PERSON><PERSON><PERSON> - Vthon</title>
    <link rel="stylesheet" href="../assets/css/styles.css">
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <style>
        .login-container {
            min-height: calc(100vh - 200px);
            display: flex;
            align-items: center;
            justify-content: center;
            background-image: url('../assets/images/background.jpg');
            background-size: cover;
            background-position: center;
            position: relative;
            padding: 100px 0;
            margin-top: 50px;
        }
        
        .login-container::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.6);
        }
        
        .login-form {
            background-color: white;
            padding: 40px;
            border-radius: 10px;
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
            width: 400px;
            max-width: 90%;
            position: relative;
            z-index: 1;
        }
        
        .login-form h2 {
            text-align: center;
            margin-bottom: 30px;
            color: #333;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: 500;
        }
        
        .form-group input {
            width: 100%;
            padding: 12px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 16px;
        }
        
        .forgot-password {
            text-align: right;
            margin-bottom: 20px;
        }
        
        .forgot-password a {
            color: #4285F4;
            text-decoration: none;
        }
        
        .login-btn {
            width: 100%;
            padding: 12px;
            background-color: #4285F4;
            color: white;
            border: none;
            border-radius: 5px;
            font-size: 16px;
            cursor: pointer;
            transition: background-color 0.3s;
        }
        
        .login-btn:hover {
            background-color: #3367D6;
        }
        
        .error-message, .success-message {
            padding: 10px;
            margin-top: 15px;
            border-radius: 5px;
            text-align: center;
            display: none;
        }
        
        .error-message {
            background-color: rgba(255, 77, 77, 0.1);
            color: #ff4d4d;
        }
        
        .success-message {
            background-color: rgba(76, 217, 100, 0.1);
            color: #2ca745;
        }
        
        .loading {
            display: none;
            text-align: center;
            margin-top: 15px;
        }
        
        .loading i {
            animation: spin 1s linear infinite;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <!-- Navigation Bar -->
    <header>
        <div class="container">
            <div class="logo">
                <img src="../assets/images/logo.jpg" alt="Vthon Logo">
            </div>
            <nav>
                <ul>
                    <li><a href="../index.html">Trang Chủ</a></li>
                    <li><a href="classes.html">Lớp Học</a></li>
                    <li><a href="achievements.html">Thành Tích</a></li>
                    <li><a href="register.html">Đăng Ký</a></li>
                    <li><a href="rankings.html">Bảng Xếp Hạng</a></li>
                    <li><a href="research.html">Nghiên Cứu</a></li>
                    <li><a href="account.html">Tài Khoản</a></li>
                </ul>
            </nav>
        </div>
    </header>
    
    <!-- Login Section -->
    <section class="login-container">
        <div class="login-form">
            <h2>Đăng Nhập</h2>
            <form id="loginForm">
                <div class="form-group">
                    <label for="username">Email</label>
                    <input type="email" id="email" name="email" required>
                </div>
                <div class="form-group">
                    <label for="password">Mật khẩu</label>
                    <input type="password" id="password" name="password" required>
                </div>
                <div class="forgot-password">
                    <a href="#" id="forgotPasswordLink">Quên mật khẩu?</a>
                </div>
                <button type="submit" class="login-btn">Đăng Nhập</button>
            </form>
            
            <div class="loading" id="loadingIndicator">
                <i class="fas fa-spinner"></i> Đang xử lý...
            </div>
            <div class="error-message" id="errorMessage"></div>
            <div class="success-message" id="successMessage"></div>
            
            <div style="margin-top: 20px; text-align: center; border-top: 1px solid #eee; padding-top: 15px;">
                <p style="color: #666; margin-bottom: 8px;">Chỉ dành cho học viên đã đăng ký</p>
                <a href="register.html" style="color: #4285F4; text-decoration: none; font-weight: 500;">Đăng ký khóa học</a>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer>
        <div class="container">
            <p>&copy; Kiến thức có đầu nhưng không có cuối - Vthon.</p>
        </div>
    </footer>

    <!-- Firebase SDK -->
    <script type="module">
        // Import the functions you need from the SDKs
        import { initializeApp } from "https://www.gstatic.com/firebasejs/11.8.1/firebase-app.js";
        import { getAnalytics } from "https://www.gstatic.com/firebasejs/11.8.1/firebase-analytics.js";
        import { 
            getAuth, 
            signInWithEmailAndPassword,
            sendPasswordResetEmail
        } from "https://www.gstatic.com/firebasejs/11.8.1/firebase-auth.js";

        // Firebase configuration
        const firebaseConfig = {
            apiKey: "AIzaSyBKNo8y_MOKYc3f3UdNRFwcMgeLRW71WXA",
            authDomain: "classroom-web-48bc2.firebaseapp.com",
            projectId: "classroom-web-48bc2",
            storageBucket: "classroom-web-48bc2.firebasestorage.app",
            messagingSenderId: "446746787502",
            appId: "1:446746787502:web:48d5ffd5a0b2c6e043b73f",
            measurementId: "G-742XRP9E96"
        };

        // Initialize Firebase
        const app = initializeApp(firebaseConfig);
        const analytics = getAnalytics(app);
        const auth = getAuth(app);
        
        // UI Elements
        const loginForm = document.getElementById('loginForm');
        const loadingIndicator = document.getElementById('loadingIndicator');
        const errorMessage = document.getElementById('errorMessage');
        const successMessage = document.getElementById('successMessage');
        const forgotPasswordLink = document.getElementById('forgotPasswordLink');
        
        // Function to display error
        function showError(message) {
            errorMessage.textContent = message;
            errorMessage.style.display = 'block';
            successMessage.style.display = 'none';
        }
        
        // Function to display success message
        function showSuccess(message) {
            successMessage.textContent = message;
            successMessage.style.display = 'block';
            errorMessage.style.display = 'none';
        }
        
        // Function to toggle loading state
        function setLoading(isLoading) {
            loadingIndicator.style.display = isLoading ? 'block' : 'none';
        }
        
        // Login form submission
        loginForm.addEventListener('submit', async (e) => {
            e.preventDefault();
            
            const email = document.getElementById('email').value;
            const password = document.getElementById('password').value;
            
            if (!email || !password) {
                showError('Vui lòng nhập đầy đủ email và mật khẩu');
                return;
            }
            
            setLoading(true);
            errorMessage.style.display = 'none';
            successMessage.style.display = 'none';
            
            try {
                await signInWithEmailAndPassword(auth, email, password);
                showSuccess('Đăng nhập thành công! Đang chuyển hướng...');
                
                // Redirect to account page after successful login
                setTimeout(() => {
                    window.location.href = 'account.html';
                }, 1000);
            } catch (error) {
                setLoading(false);
                
                switch (error.code) {
                    case 'auth/invalid-credential':
                        showError('Email hoặc mật khẩu không chính xác. Vui lòng thử lại.');
                        break;
                    case 'auth/user-disabled':
                        showError('Tài khoản này đã bị khóa. Vui lòng liên hệ quản trị viên.');
                        break;
                    case 'auth/too-many-requests':
                        showError('Quá nhiều lần thử không thành công. Vui lòng thử lại sau.');
                        break;
                    default:
                        showError(`Lỗi đăng nhập: ${error.message}`);
                }
            }
        });
        
        // Forgot password functionality
        forgotPasswordLink.addEventListener('click', async (e) => {
            e.preventDefault();
            
            const email = document.getElementById('email').value;
            
            if (!email) {
                showError('Vui lòng nhập email để đặt lại mật khẩu');
                return;
            }
            
            setLoading(true);
            
            try {
                await sendPasswordResetEmail(auth, email);
                showSuccess('Email đặt lại mật khẩu đã được gửi. Vui lòng kiểm tra hộp thư của bạn.');
                setLoading(false);
            } catch (error) {
                setLoading(false);
                
                switch (error.code) {
                    case 'auth/invalid-email':
                        showError('Email không hợp lệ');
                        break;
                    case 'auth/user-not-found':
                        showError('Không tìm thấy tài khoản với email này');
                        break;
                    default:
                        showError(`Lỗi: ${error.message}`);
                }
            }
        });
    </script>
    
    <script src="../assets/js/script.js"></script>
</body>
</html> 