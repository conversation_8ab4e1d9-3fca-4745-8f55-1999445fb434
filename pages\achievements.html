<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Th<PERSON><PERSON> - <PERSON>on</title>
    <link rel="stylesheet" href="../assets/css/styles.css">
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <style>
        .page-header {
            text-align: center;
            margin: 120px 0 50px;
        }
        
        .page-header h1 {
            font-size: 2.5rem;
            color: #333;
            margin-bottom: 15px;
        }
        
        .page-header p {
            color: #666;
            font-size: 1.2rem;
            max-width: 700px;
            margin: 0 auto;
        }
        
        .achievements-container {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 30px;
            margin: 30px 0 50px;
        }
        
        .achievement-card {
            background-color: white;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
            overflow: hidden;
            transition: transform 0.3s, box-shadow 0.3s;
            cursor: pointer;
            position: relative;
        }
        
        .achievement-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
        }
        
        .achievement-image {
            height: 200px;
            overflow: hidden;
        }
        
        .achievement-image img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            transition: transform 0.5s;
        }
        
        .achievement-card:hover .achievement-image img {
            transform: scale(1.05);
        }
        
        .achievement-details {
            padding: 20px;
        }
        
        .achievement-date {
            color: #4285F4;
            font-size: 0.9rem;
            margin-bottom: 10px;
        }
        
        .achievement-title {
            font-size: 1.5rem;
            color: #333;
            margin-bottom: 10px;
            font-weight: 700;
        }
        
        .achievement-excerpt {
            color: #666;
            margin-bottom: 15px;
            display: -webkit-box;
            -webkit-line-clamp: 3;
            -webkit-box-orient: vertical;
            overflow: hidden;
        }
        
        .read-more {
            color: #4285F4;
            font-weight: 500;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
        }
        
        .read-more i {
            margin-left: 5px;
            transition: transform 0.3s;
        }
        
        .read-more:hover i {
            transform: translateX(3px);
        }
        
        .modal {
            display: none;
            position: fixed;
            z-index: 1100;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            overflow: auto;
            background-color: rgba(0, 0, 0, 0.7);
            opacity: 0;
            transition: opacity 0.3s;
        }
        
        .modal.active {
            display: block;
            opacity: 1;
        }
        
        .modal-content {
            background-color: white;
            margin: 5% auto;
            padding: 30px;
            border-radius: 10px;
            width: 80%;
            max-width: 800px;
            box-shadow: 0 5px 30px rgba(0, 0, 0, 0.3);
            position: relative;
            transform: translateY(-50px);
            opacity: 0;
            transition: all 0.4s;
        }
        
        .modal.active .modal-content {
            transform: translateY(0);
            opacity: 1;
        }
        
        .close-modal {
            position: absolute;
            top: 15px;
            right: 15px;
            font-size: 1.5rem;
            color: #999;
            cursor: pointer;
            transition: color 0.3s;
        }
        
        .close-modal:hover {
            color: #333;
        }
        
        .modal-image {
            width: 100%;
            max-height: 400px;
            object-fit: cover;
            border-radius: 5px;
            margin-bottom: 20px;
        }
        
        .modal-title {
            font-size: 2rem;
            margin-bottom: 15px;
            color: #333;
        }
        
        .modal-date {
            color: #4285F4;
            font-size: 0.9rem;
            margin-bottom: 20px;
        }
        
        .modal-content p {
            margin-bottom: 15px;
            line-height: 1.7;
        }
        
        @media (max-width: 768px) {
            .modal-content {
                width: 95%;
                padding: 20px;
                margin: 10% auto;
            }
            
            .modal-title {
                font-size: 1.5rem;
            }
        }
    </style>
</head>
<body>
    <!-- Navigation Bar -->
    <header>
        <div class="container">
            <div class="logo">
                <img src="../assets/images/logo.jpg" alt="Vthon Logo">
            </div>
            <nav>
                <ul>
                    <li><a href="../index.html">Trang Chủ</a></li>
                    <li><a href="classes.html">Lớp Học</a></li>
                    <li><a href="achievements.html" class="active">Thành Tích</a></li>
                    <li><a href="register.html">Đăng Ký</a></li>
                    <li><a href="rankings.html">Bảng Xếp Hạng</a></li>
                    <li><a href="research.html">Nghiên Cứu</a></li>
                    <li><a href="account.html">Tài Khoản</a></li>
                </ul>
            </nav>
        </div>
    </header>

    <!-- Achievements Section -->
    <section>
        <div class="container">
            <div class="page-header">
                <h1>Thành Tích Học Viên</h1>
                <p>Tự hào về những thành tựu xuất sắc của học viên Vthon trong lĩnh vực lập trình Python và AI</p>
            </div>
            
            <div class="achievements-container">
                <!-- Achievement 1: COVID-19 App Award -->
                <div class="achievement-card" data-id="1">
                    <div class="achievement-image">
                        <img src="../assets/images/achievements/covid-app-award.jpg" alt="Giải Nhất KHKT Tỉnh Kon Tum">
                    </div>
                    <div class="achievement-details">
                        <div class="achievement-date">Năm 2021</div>
                        <h3 class="achievement-title">Giải Nhất Cuộc Thi KHKT Tỉnh Kon Tum - Ứng Dụng Hỗ Trợ Điều Trị COVID-19</h3>
                        <p class="achievement-excerpt">Dự án ứng dụng Android hỗ trợ bệnh nhân và bác sĩ trong việc điều trị COVID-19 đã đạt giải Nhất cấp tỉnh và tham gia cuộc thi KHKT quốc gia.</p>
                        <a href="#" class="read-more">Xem thêm <i class="fas fa-arrow-right"></i></a>
                    </div>
                </div>

                <!-- Placeholder for future achievements -->
                <div style="grid-column: 1/-1; text-align: center; padding: 50px; color: #666;">
                    <i class="fas fa-trophy" style="font-size: 3rem; margin-bottom: 20px; color: #ddd;"></i>
                    <h3>Thêm thành tích mới sẽ được cập nhật sớm</h3>
                    <p>Chúng tôi đang chờ đợi những thành tích xuất sắc tiếp theo từ các học viên</p>
                </div>
            </div>
        </div>
    </section>

    <!-- Modal for Achievement Details -->
    <div class="modal" id="achievementModal">
        <div class="modal-content">
            <span class="close-modal"><i class="fas fa-times"></i></span>
            <img src="" alt="" class="modal-image">
            <div class="modal-date"></div>
            <h2 class="modal-title"></h2>
            <div class="modal-body"></div>
        </div>
    </div>

    <!-- Footer -->
    <footer>
        <div class="container">
            <p>&copy; Kiến thức có đầu nhưng không có cuối - Vthon.</p>
        </div>
    </footer>

    <script src="../assets/js/script.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const achievementCards = document.querySelectorAll('.achievement-card');
            const modal = document.getElementById('achievementModal');
            const modalImage = modal.querySelector('.modal-image');
            const modalDate = modal.querySelector('.modal-date');
            const modalTitle = modal.querySelector('.modal-title');
            const modalBody = modal.querySelector('.modal-body');
            const closeModal = modal.querySelector('.close-modal');
            
            // Achievement details content
            const achievementDetails = {
                1: {
                    title: "Giải Nhất Cuộc Thi KHKT Tỉnh Kon Tum - Ứng Dụng Hỗ Trợ Điều Trị COVID-19",
                    date: "Năm 2021",
                    image: "../assets/images/achievements/covid-app-award.jpg",
                    content: `
                        <p>Dự án ứng dụng Android hỗ trợ bệnh nhân và bác sĩ trong việc điều trị COVID-19 đã đạt giải Nhất cấp tỉnh Kon Tum và được tham gia cuộc thi Khoa học Kỹ thuật cấp quốc gia.</p>

                        <p>Trong bối cảnh đại dịch COVID-19 diễn ra phức tạp, việc quản lý và theo dõi tình trạng sức khỏe của bệnh nhân trở nên vô cùng quan trọng. Ứng dụng được phát triển nhằm tạo ra một cầu nối hiệu quả giữa bệnh nhân và đội ngũ y tế, giúp tối ưu hóa quá trình điều trị và theo dõi sức khỏe.</p>

                        <p><strong>Tính năng chính của ứng dụng:</strong></p>
                        <ul>
                            <li>Theo dõi các chỉ số sức khỏe hàng ngày (nhiệt độ, SpO2, nhịp tim)</li>
                            <li>Nhắc nhở uống thuốc và thực hiện các biện pháp điều trị</li>
                            <li>Kết nối trực tiếp với bác sĩ qua video call và chat</li>
                            <li>Cảnh báo tự động khi có dấu hiệu bất thường</li>
                            <li>Quản lý hồ sơ bệnh án điện tử</li>
                            <li>Cung cấp thông tin y tế đáng tin cậy về COVID-19</li>
                        </ul>

                        <p>Ứng dụng được phát triển bằng Android Studio với Java, tích hợp Firebase để lưu trữ dữ liệu và WebRTC cho tính năng video call. Giao diện được thiết kế thân thiện, dễ sử dụng cho mọi lứa tuổi, đặc biệt quan tâm đến người cao tuổi - đối tượng dễ bị tổn thương nhất trong đại dịch.</p>

                        <p>Ban giám khảo cuộc thi KHKT tỉnh Kon Tum đánh giá cao tính thực tiễn và khả năng ứng dụng rộng rãi của dự án. Ứng dụng không chỉ hỗ trợ trong giai đoạn đại dịch mà còn có thể mở rộng cho việc quản lý sức khỏe tổng quát.</p>

                        <p>Sau khi đạt giải Nhất cấp tỉnh, dự án đã được đề cử tham gia cuộc thi Khoa học Kỹ thuật cấp quốc gia, nơi có cơ hội giao lưu và học hỏi từ các dự án xuất sắc khác trên toàn quốc.</p>

                        <p>Thành tích này không chỉ là niềm tự hào cá nhân mà còn thể hiện tinh thần sáng tạo và ứng dụng công nghệ vào việc giải quyết các vấn đề thực tiễn của xã hội, đặc biệt trong những thời điểm khó khăn như đại dịch.</p>
                    `
                }
            };
            
            // Open modal with achievement details
            achievementCards.forEach(card => {
                card.addEventListener('click', function() {
                    const achievementId = this.dataset.id;
                    const details = achievementDetails[achievementId];
                    
                    modalImage.src = details.image;
                    modalImage.alt = details.title;
                    modalDate.textContent = details.date;
                    modalTitle.textContent = details.title;
                    modalBody.innerHTML = details.content;
                    
                    modal.classList.add('active');
                    document.body.style.overflow = 'hidden';
                });
            });
            
            // Close modal
            closeModal.addEventListener('click', function() {
                modal.classList.remove('active');
                document.body.style.overflow = 'auto';
            });
            
            // Close modal when clicking outside content
            window.addEventListener('click', function(event) {
                if (event.target === modal) {
                    modal.classList.remove('active');
                    document.body.style.overflow = 'auto';
                }
            });
        });
    </script>
</body>
</html> 